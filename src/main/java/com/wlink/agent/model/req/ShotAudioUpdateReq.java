package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 分镜音频修改请求
 */
@Data
@Schema(description = "分镜音频修改请求")
public class ShotAudioUpdateReq {
    
    /**
     * 音频资源ID
     */
    @NotNull(message = "音频资源ID不能为空")
    @Schema(description = "音频资源ID", required = true)
    private Long audioId;
    
    /**
     * 音频URL
     */
    @Schema(description = "音频URL")
    private String audioUrl;
    
    /**
     * 音频类型(1-旁白,2-对话,3-背景音乐)
     */
    @Schema(description = "音频类型(1-旁白,2-对话,3-背景音乐)")
    private Integer audioType;
    
    /**
     * 音频文本，可能是旁白或对话
     */
    @Schema(description = "音频文本")
    private String text;
    
    /**
     * 声音ID
     */
    @Schema(description = "声音ID")
    private String voiceId;
    
    /**
     * 音频时长(毫秒)
     */
    @Schema(description = "音频时长(毫秒)")
    private Long audioDuration;
    
    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;
}

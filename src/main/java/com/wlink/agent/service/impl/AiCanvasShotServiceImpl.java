package com.wlink.agent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wlink.agent.client.CompletionApiClient;
import com.wlink.agent.config.ExternalApiConfig;
import com.wlink.agent.dao.mapper.AiCanvasAudioMapper;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasMapper;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
import com.wlink.agent.dao.po.AiCanvasAudioPo;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiCanvasVideoPo;
import com.wlink.agent.model.req.ShotCreateReq;
import com.wlink.agent.model.req.ShotEditReq;
import com.wlink.agent.model.req.ShotAudioCreateReq;
import com.wlink.agent.model.req.ShotAudioUpdateReq;
import com.wlink.agent.model.req.ShotAudioOrderUpdateReq;
import com.wlink.agent.model.req.ShotStatusBatchQueryReq;
import com.wlink.agent.model.req.TtsGenerateReq;
import com.wlink.agent.model.req.VideoExtensionReq;
import com.wlink.agent.model.res.ShotStatusRes;
import com.wlink.agent.model.res.AiCanvasShotRes;
import com.wlink.agent.model.res.AiCanvasAudioRes;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.model.dto.SoundEffectsResult;
import com.wlink.agent.service.AiCanvasShotService;
import com.wlink.agent.enums.ShotStatus;
import com.wlink.agent.service.AiCreationContentService;
import com.wlink.agent.service.SoundEffectsService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.TextSplitUtils;
import com.wlink.agent.utils.UserContext;
import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【ai_canvas_shot(画布分镜表)】的数据库操作Service实现
* @createDate 2025-06-23 17:39:35
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class AiCanvasShotServiceImpl extends ServiceImpl<AiCanvasShotMapper, AiCanvasShotPo>
    implements AiCanvasShotService{

    private final AiCanvasMapper canvasMapper;
    private final AiCanvasImageMapper canvasImageMapper;
    private final AiCanvasVideoMapper canvasVideoMapper;
    private final AiCanvasAudioMapper canvasAudioMapper;
    private final AiCreationContentService aiCreationContentService;
    private final SoundEffectsService soundEffectsService;
    private final CompletionApiClient completionApiClient;
    private final ExternalApiConfig externalApiConfig;
    private final OssUtils ossUtils;

    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";
    @Value("${spring.profiles.active}")
    String env;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createShot(ShotCreateReq req) {
        if (req.getCanvasId() == null) {
            throw new BizException("画布ID不能为空");
        }

        // 验证画布是否存在且属于当前用户
        AiCanvasPo canvasPo = canvasMapper.selectById(req.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("Canvas not found, id: {}", req.getCanvasId());
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to operate canvas {}", currentUserId, req.getCanvasId());
            throw new BizException("无权限操作此画布");
        }

        // 处理插入位置
        Integer insertPosition = req.getInsertPosition();
        if (insertPosition == null || insertPosition <= 0) {
            // 添加到末尾，查询当前最大排序号
            LambdaQueryWrapper<AiCanvasShotPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiCanvasShotPo::getCanvasId, req.getCanvasId())
                    .eq(AiCanvasShotPo::getDelFlag, 0)
                    .orderByDesc(AiCanvasShotPo::getSortOrder)
                    .last("LIMIT 1");

            AiCanvasShotPo lastShot = this.getOne(queryWrapper);
            insertPosition = lastShot != null ? lastShot.getSortOrder() + 1 : 1;
        } else {
            // 插入到指定位置，需要将后面的分镜顺序后移
            LambdaUpdateWrapper<AiCanvasShotPo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AiCanvasShotPo::getCanvasId, req.getCanvasId())
                    .eq(AiCanvasShotPo::getDelFlag, 0)
                    .ge(AiCanvasShotPo::getSortOrder, insertPosition)
                    .setSql("sort_order = sort_order + 1");

            this.update(updateWrapper);
        }

        // 创建新分镜
        AiCanvasShotPo shotPo = new AiCanvasShotPo();
        shotPo.setCanvasId(req.getCanvasId());
        shotPo.setCode(generateShotCode());
        shotPo.setType(req.getType());
        shotPo.setComposition(req.getComposition());
        shotPo.setMovement(req.getMovement());
        shotPo.setSortOrder(insertPosition);
        shotPo.setDisplayType(req.getDisplayType());
        shotPo.setShotStatus(ShotStatus.INITIAL.getValue()); // 默认为初始状态
        shotPo.setCreateTime(new Date());
        shotPo.setUpdateTime(new Date());
        shotPo.setDelFlag(0);

        this.save(shotPo);

        log.info("Created new shot for canvas {}, shot id: {}, position: {}",
                req.getCanvasId(), shotPo.getId(), insertPosition);

        return shotPo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editShot(ShotEditReq req) {
        if (req.getId() == null) {
            throw new BizException("分镜ID不能为空");
        }

        // 查询分镜是否存在
        AiCanvasShotPo shotPo = this.getById(req.getId());
        if (shotPo == null || shotPo.getDelFlag() == 1) {
            log.error("Shot not found, id: {}", req.getId());
            throw new BizException("分镜不存在");
        }

        // 验证画布权限
        AiCanvasPo canvasPo = canvasMapper.selectById(shotPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to edit shot {}", currentUserId, req.getId());
            throw new BizException("无权限编辑此分镜");
        }
        shotPo.setType(req.getType());
        shotPo.setComposition(req.getComposition());
        shotPo.setMovement(req.getMovement());
        shotPo.setDisplayType(req.getDisplayType());
        shotPo.setUpdateTime(new Date());
        this.updateById(shotPo);
        // 更新图片信息
        updateImageInfo(shotPo, req);

        // 更新视频信息
        updateVideoInfo(shotPo, req);

        log.info("Updated shot {}", req.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteShot(Long shotId) {
        if (shotId == null) {
            throw new BizException("分镜ID不能为空");
        }
        // 查询分镜是否存在
        AiCanvasShotPo shotPo = this.getById(shotId);
        if (shotPo == null || shotPo.getDelFlag() == 1) {
            log.error("Shot not found, id: {}", shotId);
            throw new BizException("分镜不存在");
        }
        // 验证画布权限
        AiCanvasPo canvasPo = canvasMapper.selectById(shotPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }
        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to delete shot {}", currentUserId, shotId);
            throw new BizException("无权限删除此分镜");
        }

        // 记录被删除分镜的排序号
        Integer deletedSortOrder = shotPo.getSortOrder();

        this.removeById(shotPo.getId());

        // 软删除相关资源
        String shotCode = shotPo.getCode();

        // 删除图片资源
        LambdaUpdateWrapper<AiCanvasImagePo> imageUpdateWrapper = new LambdaUpdateWrapper<>();
        imageUpdateWrapper.eq(AiCanvasImagePo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasImagePo::getShotCode, shotCode)
                .set(AiCanvasImagePo::getDelFlag, 1)
                .set(AiCanvasImagePo::getUpdateTime, new Date());
        canvasImageMapper.update(null, imageUpdateWrapper);

        // 删除视频资源
        LambdaUpdateWrapper<AiCanvasVideoPo> videoUpdateWrapper = new LambdaUpdateWrapper<>();
        videoUpdateWrapper.eq(AiCanvasVideoPo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasVideoPo::getShotCode, shotCode)
                .set(AiCanvasVideoPo::getDelFlag, 1)
                .set(AiCanvasVideoPo::getUpdateTime, new Date());
        canvasVideoMapper.update(null, videoUpdateWrapper);

        // 删除音频资源
        LambdaUpdateWrapper<AiCanvasAudioPo> audioUpdateWrapper = new LambdaUpdateWrapper<>();
        audioUpdateWrapper.eq(AiCanvasAudioPo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasAudioPo::getShotCode, shotCode)
                .set(AiCanvasAudioPo::getDelFlag, 1)
                .set(AiCanvasAudioPo::getUpdateTime, new Date());
        canvasAudioMapper.update(null, audioUpdateWrapper);

        // 重新整理分镜顺序：将被删除分镜后面的所有分镜顺序减1
        if (deletedSortOrder != null) {
            LambdaUpdateWrapper<AiCanvasShotPo> reorderWrapper = new LambdaUpdateWrapper<>();
            reorderWrapper.eq(AiCanvasShotPo::getCanvasId, shotPo.getCanvasId())
                    .eq(AiCanvasShotPo::getDelFlag, 0)
                    .gt(AiCanvasShotPo::getSortOrder, deletedSortOrder)
                    .setSql("sort_order = sort_order - 1")
                    .set(AiCanvasShotPo::getUpdateTime, new Date());

            this.update(reorderWrapper);
            log.info("Reordered shots after deleting shot {} with sort order {}", shotId, deletedSortOrder);
        }

        log.info("Deleted shot {} and its resources", shotId);
    }

    /**
     * 生成分镜编码
     */
    private String generateShotCode() {
        return "SHOT-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
    }

    /**
     * 更新图片信息
     */
    private void updateImageInfo(AiCanvasShotPo shotPo, ShotEditReq req) {
        // 检查是否有图片信息需要更新
        boolean hasImageInfo = StringUtils.hasText(req.getImageUrl()) ||
                StringUtils.hasText(req.getImagePrompt());


        if (!hasImageInfo) {
            return;
        }

        // 查询是否已存在图片资源
        LambdaQueryWrapper<AiCanvasImagePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasImagePo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasImagePo::getShotCode, shotPo.getCode())
                .eq(AiCanvasImagePo::getDelFlag, 0);

        AiCanvasImagePo imagePo = canvasImageMapper.selectOne(queryWrapper);

        if (imagePo == null) {
            // 创建新的图片资源
            imagePo = new AiCanvasImagePo();
            imagePo.setCanvasId(shotPo.getCanvasId());
            imagePo.setShotCode(shotPo.getCode());
            imagePo.setCreateTime(new Date());
            imagePo.setDelFlag(0);
        }

        // 更新图片信息
        if (StringUtils.hasText(req.getImageUrl())) {
            imagePo.setImageUrl(req.getImageUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX,""));
        }
        if (StringUtils.hasText(req.getImagePrompt())) {
            imagePo.setImagePrompt(req.getImagePrompt());
        }
        if (StringUtils.hasText(req.getImageDesc())) {
            imagePo.setImageDesc(req.getImageDesc());
        }
        if (StringUtils.hasText(req.getImageAspectRatio())) {
            imagePo.setImageAspectRatio(req.getImageAspectRatio());
        }
        if (StringUtils.hasText(req.getImageStatus())) {
            imagePo.setImageStatus(req.getImageStatus());
        }
        if (StringUtils.hasText(req.getReferenceImage())) {
            imagePo.setReferenceImage(req.getReferenceImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX,""));
        }

        imagePo.setUpdateTime(new Date());

        if (imagePo.getId() == null) {
            canvasImageMapper.insert(imagePo);
        } else {
            canvasImageMapper.updateById(imagePo);
        }
        shotPo.setShotStatus(ShotStatus.COMPLETED.getValue());
        shotPo.setUpdateTime(new Date());
        this.updateById(shotPo);

    }

    /**
     * 更新视频信息
     */
    private void updateVideoInfo(AiCanvasShotPo shotPo, ShotEditReq req) {
        // 检查是否有视频信息需要更新
        boolean hasVideoInfo = StringUtils.hasText(req.getVideoUrl()) ||
                StringUtils.hasText(req.getVideoPrompt());

        if (!hasVideoInfo) {
            return;
        }

        // 查询是否已存在视频资源
        LambdaQueryWrapper<AiCanvasVideoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasVideoPo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasVideoPo::getShotCode, shotPo.getCode())
                .eq(AiCanvasVideoPo::getDelFlag, 0);

        AiCanvasVideoPo videoPo = canvasVideoMapper.selectOne(queryWrapper);

        if (videoPo == null) {
            // 创建新的视频资源
            videoPo = new AiCanvasVideoPo();
            videoPo.setCanvasId(shotPo.getCanvasId());
            videoPo.setShotCode(shotPo.getCode());
            videoPo.setCreateTime(new Date());
            videoPo.setDelFlag(0);
        }

        // 更新视频信息
        if (StringUtils.hasText(req.getVideoUrl())) {
            videoPo.setVideoUrl(req.getVideoUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX,""));
        }
        if (StringUtils.hasText(req.getVideoPrompt())) {
            videoPo.setVideoPrompt(req.getVideoPrompt());
        }
        if (StringUtils.hasText(req.getVideoDesc())) {
            videoPo.setVideoDesc(req.getVideoDesc());
        }
        if (req.getVideoDuration() != null) {
            videoPo.setVideoDuration(req.getVideoDuration());
        }
        if (StringUtils.hasText(req.getVideoAspectRatio())) {
            videoPo.setVideoAspectRatio(req.getVideoAspectRatio());
        }
        if (StringUtils.hasText(req.getVideoStatus())) {
            videoPo.setVideoStatus(req.getVideoStatus());
        }
        if (StringUtils.hasText(req.getStartFrameImage())) {
            videoPo.setStartFrameImage(req.getStartFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
        }
        if (StringUtils.hasText(req.getEndFrameImage())) {
            videoPo.setEndFrameImage(req.getEndFrameImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
        }

        videoPo.setUpdateTime(new Date());

        if (videoPo.getId() == null) {
            canvasVideoMapper.insert(videoPo);
        } else {
            canvasVideoMapper.updateById(videoPo);
        }
        shotPo.setShotStatus(ShotStatus.COMPLETED.getValue());
        shotPo.setUpdateTime(new Date());
        this.updateById(shotPo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createShotAudio(ShotAudioCreateReq req) {
        if (req.getShotId() == null) {
            throw new BizException("分镜ID不能为空");
        }

        // 验证分镜是否存在
        AiCanvasShotPo shotPo = this.getById(req.getShotId());
        if (shotPo == null || shotPo.getDelFlag() == 1) {
            log.error("Shot not found, id: {}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 验证画布权限
        AiCanvasPo canvasPo = canvasMapper.selectById(shotPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to operate shot {}", currentUserId, req.getShotId());
            throw new BizException("无权限操作此分镜");
        }

        // 处理排序序号
        Integer sortOrder = req.getSortOrder();
        if (sortOrder == null || sortOrder <= 0) {
            // 查询当前分镜下音频的最大排序号
            LambdaQueryWrapper<AiCanvasAudioPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiCanvasAudioPo::getCanvasId, shotPo.getCanvasId())
                    .eq(AiCanvasAudioPo::getShotCode, shotPo.getCode())
                    .eq(AiCanvasAudioPo::getDelFlag, 0)
                    .orderByDesc(AiCanvasAudioPo::getSortOrder)
                    .last("LIMIT 1");

            AiCanvasAudioPo lastAudio = canvasAudioMapper.selectOne(queryWrapper);
            sortOrder = lastAudio != null ? lastAudio.getSortOrder() + 1 : 1;
        }
        TtsGenerateRes ttsGenerateRes = null;
        if (!StringUtils.hasText(req.getAudioUrl())){
            if (Objects.equals(req.getAudioType(),1)){
                TtsGenerateReq ttsGenerateReq = new TtsGenerateReq();
                ttsGenerateReq.setConversationId(shotPo.getCode());
                ttsGenerateReq.setText(req.getText());
                ttsGenerateReq.setVoiceId(req.getVoiceId());
                ttsGenerateReq.setContentId(shotPo.getId().toString());
                ttsGenerateReq.setSource(2);
                ttsGenerateReq.setPitch(req.getPitch());
                ttsGenerateReq.setRate(req.getSpeed());
                ttsGenerateReq.setVolume(req.getVol());
                ttsGenerateReq.setEmotion(req.getEmotion());
                ttsGenerateReq.setTone(req.getTone());
                switch (req.getAudioType()){
                    case 1:
                        ttsGenerateReq.setSoundType("narration");
                        break;
                    case 2:
                        ttsGenerateReq.setSoundType("dialogue");
                        break;
                    case 3:
                        ttsGenerateReq.setSoundType("background");
                        break;
                    default:
                        throw new BizException("音频类型错误");
                }
                ttsGenerateRes = aiCreationContentService.generateTts(ttsGenerateReq);
            }else if (Objects.equals(req.getAudioType(),2)){

                // FLUX API 请求参数
                String string = completionApiClient.requestCompletion(req.getPrompt(), currentUserId, externalApiConfig.getTextTranslateApiKey());
                log.info("重绘提示词翻译结果：{}", string);


                // 调用音效生成服务
                // conversationId传ai_canvas表中的code, contentId传ai_canvas_shot表中的code, index传1, source传2
                SoundEffectsResult soundEffectResult = soundEffectsService.generateSoundEffectForCanvas(
                        string, // prompt使用音频文本
                        req.getAudioDuration().intValue()/1000, // 音频时长转换为秒, // duration使用音频时长
                        canvasPo.getCode(), // conversationId传画布code
                        shotPo.getCode(), // contentId传分镜code
                        1 // index传1
                );

                // 将音效结果设置到ttsGenerateRes中以便后续处理
                if (soundEffectResult != null && soundEffectResult.isValid()) {
                    ttsGenerateRes = new TtsGenerateRes();
                    ttsGenerateRes.setAudioUrl(soundEffectResult.getAudioUrl());
                    // 音效时长需要从文件信息中获取，这里暂时设置为30秒的毫秒数
                    ttsGenerateRes.setDuration(30000L);
                    log.info("音效生成成功: shotId={}, audioUrl={}", req.getShotId(), soundEffectResult.getAudioUrl());
                } else {
                    log.error("音效生成失败: shotId={}", req.getShotId());
                    throw new BizException("音效生成失败");
                }
            }
        }
        // 创建音频资源
        AiCanvasAudioPo audioPo = new AiCanvasAudioPo();
        audioPo.setCanvasId(shotPo.getCanvasId());
        audioPo.setShotCode(shotPo.getCode());
        audioPo.setAudioUrl(req.getAudioUrl());
        audioPo.setAudioType(req.getAudioType());
        audioPo.setText(req.getText());
        audioPo.setVoiceId(req.getVoiceId());
        audioPo.setAudioDuration(req.getAudioDuration());
        if (null != ttsGenerateRes){
            audioPo.setAudioUrl(ttsGenerateRes.getAudioUrl());
            audioPo.setAudioDuration(ttsGenerateRes.getDuration());
        }
        audioPo.setSortOrder(sortOrder);
        audioPo.setCreateTime(new Date());
        audioPo.setUpdateTime(new Date());
        audioPo.setDelFlag(0);
        canvasAudioMapper.insert(audioPo);
        log.info("Created audio for shot {}, audio id: {}", req.getShotId(), audioPo.getId());

        return audioPo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShotAudio(ShotAudioUpdateReq req) {
        if (req.getAudioId() == null) {
            throw new BizException("音频资源ID不能为空");
        }

        // 查询音频资源是否存在
        AiCanvasAudioPo audioPo = canvasAudioMapper.selectById(req.getAudioId());
        if (audioPo == null || audioPo.getDelFlag() == 1) {
            log.error("Audio not found, id: {}", req.getAudioId());
            throw new BizException("音频资源不存在");
        }

        // 验证画布权限
        AiCanvasPo canvasPo = canvasMapper.selectById(audioPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to update audio {}", currentUserId, req.getAudioId());
            throw new BizException("无权限修改此音频");
        }

        // 更新音频信息
        boolean needUpdate = false;
        if (StringUtils.hasText(req.getAudioUrl())) {
            audioPo.setAudioUrl(req.getAudioUrl());
            needUpdate = true;
        }
        if (req.getAudioType() != null) {
            audioPo.setAudioType(req.getAudioType());
            needUpdate = true;
        }
        if (StringUtils.hasText(req.getText())) {
            audioPo.setText(req.getText());
            needUpdate = true;
        }
        if (StringUtils.hasText(req.getVoiceId())) {
            audioPo.setVoiceId(req.getVoiceId());
            needUpdate = true;
        }
        if (req.getAudioDuration() != null) {
            audioPo.setAudioDuration(req.getAudioDuration());
            needUpdate = true;
        }
        if (req.getSortOrder() != null) {
            audioPo.setSortOrder(req.getSortOrder());
            needUpdate = true;
        }

        if (needUpdate) {
            audioPo.setUpdateTime(new Date());
            canvasAudioMapper.updateById(audioPo);
        }

        log.info("Updated audio {}", req.getAudioId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteShotAudio(Long audioId) {
        if (audioId == null) {
            throw new BizException("音频资源ID不能为空");
        }

        // 查询音频资源是否存在
        AiCanvasAudioPo audioPo = canvasAudioMapper.selectById(audioId);
        if (audioPo == null || audioPo.getDelFlag() == 1) {
            log.error("Audio not found, id: {}", audioId);
            throw new BizException("音频资源不存在");
        }

        // 验证画布权限
        AiCanvasPo canvasPo = canvasMapper.selectById(audioPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to delete audio {}", currentUserId, audioId);
            throw new BizException("无权限删除此音频");
        }

        canvasAudioMapper.deleteById(audioPo.getId());

        log.info("Deleted audio {}", audioId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShotAudioOrder(ShotAudioOrderUpdateReq req) {
        if (req.getShotId() == null) {
            throw new BizException("分镜ID不能为空");
        }

        if (req.getAudioOrders() == null || req.getAudioOrders().isEmpty()) {
            throw new BizException("音频顺序列表不能为空");
        }

        // 验证分镜是否存在
        AiCanvasShotPo shotPo = this.getById(req.getShotId());
        if (shotPo == null || shotPo.getDelFlag() == 1) {
            log.error("Shot not found, id: {}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 验证画布权限
        AiCanvasPo canvasPo = canvasMapper.selectById(shotPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to update shot {}", currentUserId, req.getShotId());
            throw new BizException("无权限操作此分镜");
        }

        // 获取要更新的音频ID列表
        List<Long> audioIds = req.getAudioOrders().stream()
                .map(ShotAudioOrderUpdateReq.AudioOrderItem::getAudioId)
                .collect(Collectors.toList());

        // 验证音频是否都属于该分镜
        LambdaQueryWrapper<AiCanvasAudioPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasAudioPo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasAudioPo::getShotCode, shotPo.getCode())
                .in(AiCanvasAudioPo::getId, audioIds)
                .eq(AiCanvasAudioPo::getDelFlag, 0);

        List<AiCanvasAudioPo> existingAudios = canvasAudioMapper.selectList(queryWrapper);

        if (existingAudios.size() != audioIds.size()) {
            log.error("Some audios not found or not belong to shot {}", req.getShotId());
            throw new BizException("部分音频不存在或不属于该分镜");
        }

        // 批量更新音频顺序
        for (ShotAudioOrderUpdateReq.AudioOrderItem orderItem : req.getAudioOrders()) {
            AiCanvasAudioPo audioPo = new AiCanvasAudioPo();
            audioPo.setId(orderItem.getAudioId());
            audioPo.setSortOrder(orderItem.getSortOrder());
            audioPo.setUpdateTime(new Date());
            canvasAudioMapper.updateById(audioPo);
        }

        log.info("Updated audio order for shot {}, updated {} audios", req.getShotId(), req.getAudioOrders().size());
    }

    @Override
    public List<ShotStatusRes> batchQueryShotStatus(ShotStatusBatchQueryReq req) {
        if (req.getShotIds() == null || req.getShotIds().isEmpty()) {
            throw new BizException("分镜ID列表不能为空");
        }

        // 查询分镜状态
        LambdaQueryWrapper<AiCanvasShotPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AiCanvasShotPo::getId, req.getShotIds())
                .eq(AiCanvasShotPo::getDelFlag, 0)
                .select(AiCanvasShotPo::getId, AiCanvasShotPo::getShotStatus, AiCanvasShotPo::getCanvasId);

        List<AiCanvasShotPo> shotList = this.list(queryWrapper);

        // 验证权限 - 检查所有分镜是否属于当前用户的画布
        if (!shotList.isEmpty()) {
            String currentUserId = UserContext.getUser().getUserId();

            // 获取所有画布ID
            List<Long> canvasIds = shotList.stream()
                    .map(AiCanvasShotPo::getCanvasId)
                    .distinct()
                    .collect(Collectors.toList());

            // 验证画布权限
            LambdaQueryWrapper<AiCanvasPo> canvasQueryWrapper = new LambdaQueryWrapper<>();
            canvasQueryWrapper.in(AiCanvasPo::getId, canvasIds)
                    .eq(AiCanvasPo::getUserId, currentUserId)
                    .eq(AiCanvasPo::getDelFlag, 0)
                    .select(AiCanvasPo::getId);

            List<AiCanvasPo> userCanvasList = canvasMapper.selectList(canvasQueryWrapper);
            List<Long> userCanvasIds = userCanvasList.stream()
                    .map(AiCanvasPo::getId)
                    .collect(Collectors.toList());

            // 过滤出用户有权限的分镜
            shotList = shotList.stream()
                    .filter(shot -> userCanvasIds.contains(shot.getCanvasId()))
                    .collect(Collectors.toList());
        }

        // 转换为响应对象
        List<ShotStatusRes> result = shotList.stream()
                .map(shot -> new ShotStatusRes(shot.getId(), shot.getShotStatus()))
                .collect(Collectors.toList());



        // 对于未找到的分镜ID，返回null状态
        List<Long> foundShotIds = result.stream()
                .map(ShotStatusRes::getShotId)
                .collect(Collectors.toList());

        for (Long shotId : req.getShotIds()) {
            if (!foundShotIds.contains(shotId)) {
                result.add(new ShotStatusRes(shotId, null));
            }
        }

        log.info("Batch queried shot status for {} shots, found {} valid shots",
                req.getShotIds().size(), shotList.size());

        return result;
    }

    @Override
    public AiCanvasShotRes getShotDetail(Long shotId) {
        if (shotId == null) {
            throw new BizException("分镜ID不能为空");
        }

        // 查询分镜基本信息
        AiCanvasShotPo shotPo = this.getById(shotId);
        if (shotPo == null || shotPo.getDelFlag() == 1) {
            log.error("Shot not found, id: {}", shotId);
            throw new BizException("分镜不存在");
        }

        // 验证画布权限
        AiCanvasPo canvasPo = canvasMapper.selectById(shotPo.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to view shot {}", currentUserId, shotId);
            throw new BizException("无权限查看此分镜");
        }

        // 转换为响应对象
        AiCanvasShotRes shotRes = convertToShotRes(shotPo);

        // 查询并设置图片信息
        LambdaQueryWrapper<AiCanvasImagePo> imageQueryWrapper = new LambdaQueryWrapper<>();
        imageQueryWrapper.eq(AiCanvasImagePo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasImagePo::getShotCode, shotPo.getCode())
                .eq(AiCanvasImagePo::getDelFlag, 0);

        AiCanvasImagePo imagePo = canvasImageMapper.selectOne(imageQueryWrapper);
        if (imagePo != null) {
            shotRes.setImageUrl(MediaUrlPrefixUtil.getMediaUrl(imagePo.getImageUrl()));
            shotRes.setImagePrompt(imagePo.getImagePrompt());
            shotRes.setImageDesc(imagePo.getImageDesc());
            shotRes.setImageAspectRatio(imagePo.getImageAspectRatio());
            shotRes.setImageStatus(imagePo.getImageStatus());
            shotRes.setReferenceImage(imagePo.getReferenceImage());
        }

        // 查询并设置视频信息
        LambdaQueryWrapper<AiCanvasVideoPo> videoQueryWrapper = new LambdaQueryWrapper<>();
        videoQueryWrapper.eq(AiCanvasVideoPo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasVideoPo::getShotCode, shotPo.getCode())
                .eq(AiCanvasVideoPo::getDelFlag, 0);

        AiCanvasVideoPo videoPo = canvasVideoMapper.selectOne(videoQueryWrapper);
        if (videoPo != null) {
            shotRes.setVideoUrl(MediaUrlPrefixUtil.getMediaUrl(videoPo.getVideoUrl()));
            shotRes.setVideoPrompt(videoPo.getVideoPrompt());
            shotRes.setVideoDesc(videoPo.getVideoDesc());
            shotRes.setVideoDuration(videoPo.getVideoDuration());
            shotRes.setVideoAspectRatio(videoPo.getVideoAspectRatio());
            shotRes.setVideoStatus(videoPo.getVideoStatus());
            shotRes.setStartFrameImage(videoPo.getStartFrameImage());
            shotRes.setEndFrameImage(videoPo.getEndFrameImage());
        }

        // 查询并设置音频信息列表
        LambdaQueryWrapper<AiCanvasAudioPo> audioQueryWrapper = new LambdaQueryWrapper<>();
        audioQueryWrapper.eq(AiCanvasAudioPo::getCanvasId, shotPo.getCanvasId())
                .eq(AiCanvasAudioPo::getShotCode, shotPo.getCode())
                .eq(AiCanvasAudioPo::getDelFlag, 0)
                .orderByAsc(AiCanvasAudioPo::getSortOrder);

        List<AiCanvasAudioPo> audioList = canvasAudioMapper.selectList(audioQueryWrapper);
        List<AiCanvasAudioRes> audioResList = audioList.stream()
                .map(this::convertToAudioRes)
                .collect(Collectors.toList());

        shotRes.setAudios(audioResList);

        log.info("Retrieved shot detail for shot {}", shotId);

        return shotRes;
    }

    /**
     * 将分镜PO转换为分镜响应DTO
     */
    private AiCanvasShotRes convertToShotRes(AiCanvasShotPo shotPo) {
        AiCanvasShotRes res = new AiCanvasShotRes();
        BeanUtils.copyProperties(shotPo, res);
        return res;
    }

    /**
     * 将音频PO转换为音频响应DTO
     */
    private AiCanvasAudioRes convertToAudioRes(AiCanvasAudioPo audioPo) {
        AiCanvasAudioRes res = new AiCanvasAudioRes();
        BeanUtils.copyProperties(audioPo, res);
        res.setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(audioPo.getAudioUrl()));
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long extendVideo(VideoExtensionReq req) {
        log.info("开始处理视频延长请求，分镜ID: {}", req.getShotId());

        // 1. 验证分镜是否存在
        AiCanvasShotPo currentShot = this.getById(req.getShotId());
        if (currentShot == null || currentShot.getDelFlag() == 1) {
            log.error("分镜不存在，ID: {}", req.getShotId());
            throw new BizException("分镜不存在");
        }

        // 2. 验证权限
        AiCanvasPo canvasPo = canvasMapper.selectById(currentShot.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("画布不存在，ID: {}", currentShot.getCanvasId());
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("用户 {} 无权限操作画布 {}", currentUserId, currentShot.getCanvasId());
            throw new BizException("无权限操作此画布");
        }

        // 3. 获取当前分镜的视频信息
        AiCanvasVideoPo videoPo = getVideoByShot(currentShot);
        if (videoPo == null) {
            log.error("当前分镜没有视频素材，分镜ID: {}", req.getShotId());
            throw new BizException("当前分镜没有视频素材");
        }

        // 4. 获取当前分镜的音频信息
        List<AiCanvasAudioPo> audioList = getAudioListByShot(currentShot);
        if (audioList.isEmpty()) {
            log.info("当前分镜没有音频，直接创建新分镜");
            return createNextShotWithImage(currentShot, req.getLastFrameImageUrl());
        }

        // 5. 计算音频总时长
        long totalAudioDuration = audioList.stream()
                .mapToLong(audio -> audio.getAudioDuration() != null ? audio.getAudioDuration() : 0L)
                .sum();

        long videoDuration = videoPo.getVideoDuration() != null ? videoPo.getVideoDuration() : 0L;

        log.info("当前分镜音频总时长: {}ms, 视频时长: {}ms", totalAudioDuration, videoDuration);

        // 6. 判断是否需要拆分音频
        if (totalAudioDuration <= videoDuration) {
            log.info("音频时长不超过视频时长，直接创建新分镜");
            return createNextShotWithImage(currentShot, req.getLastFrameImageUrl());
        }

        // 7. 需要拆分音频
        log.info("音频时长超过视频时长，开始拆分音频处理");
        return processAudioSplitAndCreateNextShot(currentShot, audioList, videoDuration, req.getLastFrameImageUrl());
    }

    /**
     * 获取分镜的视频信息
     */
    private AiCanvasVideoPo getVideoByShot(AiCanvasShotPo shot) {
        LambdaQueryWrapper<AiCanvasVideoPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasVideoPo::getCanvasId, shot.getCanvasId())
                .eq(AiCanvasVideoPo::getShotCode, shot.getCode())
                .eq(AiCanvasVideoPo::getDelFlag, 0)
                .last("LIMIT 1");
        return canvasVideoMapper.selectOne(queryWrapper);
    }

    /**
     * 获取分镜的音频列表
     */
    private List<AiCanvasAudioPo> getAudioListByShot(AiCanvasShotPo shot) {
        LambdaQueryWrapper<AiCanvasAudioPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasAudioPo::getCanvasId, shot.getCanvasId())
                .eq(AiCanvasAudioPo::getShotCode, shot.getCode())
                .eq(AiCanvasAudioPo::getDelFlag, 0)
                .orderByAsc(AiCanvasAudioPo::getSortOrder);
        return canvasAudioMapper.selectList(queryWrapper);
    }

    /**
     * 创建下一个分镜（使用图片素材）
     */
    private Long createNextShotWithImage(AiCanvasShotPo currentShot, String imageUrl) {
        log.info("创建下一个分镜，使用图片素材: {}", imageUrl);

        // 1. 创建新分镜
        AiCanvasShotPo newShot = new AiCanvasShotPo();
        newShot.setCanvasId(currentShot.getCanvasId());
        newShot.setCode(generateShotCode(currentShot.getCanvasId()));
        newShot.setType("image");
        newShot.setComposition(currentShot.getComposition());
        newShot.setDisplayType("cover");
        newShot.setMovement("zoom-in");
        newShot.setSortOrder(currentShot.getSortOrder() + 1);
        newShot.setShotStatus(ShotStatus.COMPLETED.getValue());
        newShot.setCreateTime(new Date());
        newShot.setUpdateTime(new Date());
        newShot.setDelFlag(0);

        // 更新后续分镜的排序
        updateSubsequentShotOrders(currentShot.getCanvasId(), currentShot.getSortOrder());

        this.save(newShot);
        log.info("新分镜创建成功，ID: {}, Code: {}", newShot.getId(), newShot.getCode());

        // 2. 获取当前分镜的视频宽高比
        AiCanvasVideoPo videoPo = getVideoByShot(currentShot);
        String aspectRatio = "16:9"; // 默认宽高比
        if (videoPo != null && StringUtils.hasText(videoPo.getVideoAspectRatio())) {
            aspectRatio = videoPo.getVideoAspectRatio();
            log.info("从当前分镜视频获取宽高比: {}", aspectRatio);
        } else {
            log.warn("当前分镜没有视频或视频没有宽高比信息，使用默认宽高比: {}", aspectRatio);
        }

        // 3. 上传图片到OSS
        String image = ossUtils.uploadFile(imageUrl, OSS_PATH.replace("{env}", env)
                .replace("{userId}", UserContext.getUser().getUserId())
                .replace("{type}", "image") + IdUtil.fastSimpleUUID() + ".png");

        // 4. 创建图片素材
        AiCanvasImagePo imagePo = new AiCanvasImagePo();
        imagePo.setCanvasId(newShot.getCanvasId());
        imagePo.setShotCode(newShot.getCode());
        imagePo.setImageUrl(image);
        imagePo.setImageAspectRatio(aspectRatio); // 设置从当前分镜视频获取的宽高比
        imagePo.setImageDesc("视频延长生成的图片");
        imagePo.setImageStatus("COMPLETED");
        imagePo.setCreateTime(new Date());
        imagePo.setUpdateTime(new Date());
        imagePo.setDelFlag(0);

        canvasImageMapper.insert(imagePo);
        log.info("新分镜图片素材创建成功，分镜Code: {}, 宽高比: {}", newShot.getCode(), aspectRatio);

        return newShot.getId();
    }

    /**
     * 处理音频拆分并创建下一个分镜
     */
    private Long processAudioSplitAndCreateNextShot(AiCanvasShotPo currentShot, List<AiCanvasAudioPo> audioList,
                                                   long videoDuration, String imageUrl) {
        log.info("开始处理音频拆分，当前分镜: {}, 视频时长: {}ms", currentShot.getCode(), videoDuration);

        // 1. 删除当前分镜的所有音频
        deleteCurrentShotAudios(currentShot);

        // 2. 处理每个音频的拆分，按照sortOrder顺序处理
        List<AiCanvasAudioPo> currentShotAudios = new ArrayList<>();
        List<AiCanvasAudioPo> nextShotAudios = new ArrayList<>();

        long currentDuration = 0L;
        boolean hasReachedLimit = false;

        for (AiCanvasAudioPo audio : audioList) {
            long audioDuration = audio.getAudioDuration() != null ? audio.getAudioDuration() : 0L;

            // 如果已经达到时长限制，后续所有音频都放到下一个分镜
            if (hasReachedLimit) {
                nextShotAudios.add(audio);
                continue;
            }

            // 如果是旁白音频且会超出时长限制，需要拆分
            if (audio.getAudioType() == 1 && currentDuration + audioDuration > videoDuration) {
                // 处理旁白音频的拆分
                long remainingDuration = videoDuration - currentDuration;
                processNarrationAudioSplit(audio, remainingDuration, currentShotAudios, nextShotAudios);
                hasReachedLimit = true;
                continue;
            }

            // 如果当前音频可以完全放入当前分镜
            if (currentDuration + audioDuration <= videoDuration) {
                currentShotAudios.add(audio);
                currentDuration += audioDuration;
            } else {
                // 无法放入当前分镜，放到下一个分镜
                nextShotAudios.add(audio);
                hasReachedLimit = true;
            }
        }

        // 3. 重新生成当前分镜的音频
        regenerateAudiosForShot(currentShot, currentShotAudios);

        // 4. 创建下一个分镜
        Long nextShotId = createNextShotWithImage(currentShot, imageUrl);
        AiCanvasShotPo nextShot = this.getById(nextShotId);

        // 5. 为下一个分镜生成音频
        regenerateAudiosForShot(nextShot, nextShotAudios);

        log.info("音频拆分处理完成，下一个分镜ID: {}", nextShotId);
        return nextShotId;
    }

    /**
     * 删除当前分镜的所有音频
     */
    private void deleteCurrentShotAudios(AiCanvasShotPo shot) {
        LambdaUpdateWrapper<AiCanvasAudioPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiCanvasAudioPo::getCanvasId, shot.getCanvasId())
                .eq(AiCanvasAudioPo::getShotCode, shot.getCode())
                .set(AiCanvasAudioPo::getDelFlag, 1);
        canvasAudioMapper.update(null, updateWrapper);
        log.info("删除分镜 {} 的所有音频", shot.getCode());
    }

    /**
     * 处理旁白音频的拆分
     */
    private void processNarrationAudioSplit(AiCanvasAudioPo audio, long remainingDuration,
                                           List<AiCanvasAudioPo> currentShotAudios,
                                           List<AiCanvasAudioPo> nextShotAudios) {
        String text = audio.getText();
        if (!StringUtils.hasText(text)) {
            log.warn("音频文本为空，无法拆分，将整个音频放到下一个分镜");
            nextShotAudios.add(audio);
            return;
        }

        log.info("开始拆分旁白音频，原文本: {}, 剩余时长: {}ms", text, remainingDuration);

        // 使用工具类拆分文本
        List<String> textSegments = TextSplitUtils.splitTextByPunctuation(text);
        log.info("文本拆分完成，共 {} 个段落", textSegments.size());

        // 如果无法拆分（只有一个段落），直接放到下一个分镜
        if (textSegments.size() <= 1) {
            log.info("文本无法拆分，将整个音频放到下一个分镜");
            nextShotAudios.add(audio);
            return;
        }

        // 按顺序生成每个段落的音频，保留生成的音频用于后续使用
        List<AiCanvasAudioPo> generatedAudios = new ArrayList<>();
        long accumulatedDuration = 0L;
        int splitIndex = 0;

        for (int i = 0; i < textSegments.size(); i++) {
            String segment = textSegments.get(i);
            log.info("正在生成第 {} 个段落的音频: {}", i + 1, segment);

            // 创建新的音频对象
            AiCanvasAudioPo segmentAudio = createAudioFromOriginal(audio, segment);

            try {
                // 调用TTS生成音频
                TtsGenerateReq ttsReq = new TtsGenerateReq();
                ttsReq.setConversationId(audio.getShotCode()); // 使用分镜code作为conversationId
                ttsReq.setText(segment);
                ttsReq.setVoiceId(audio.getVoiceId());
                ttsReq.setContentId(audio.getShotCode()); // 使用分镜code作为contentId
                ttsReq.setSource(2);
                ttsReq.setSoundType("narration");
                // 使用默认的音频参数，因为AiCanvasAudioPo中没有存储这些参数
                // 如果需要自定义参数，可以从其他地方获取或使用默认值

                TtsGenerateRes ttsRes = aiCreationContentService.generateTts(ttsReq);
                if (ttsRes != null && ttsRes.getDuration() != null) {
                    // 设置生成的音频URL和实际时长
                    segmentAudio.setAudioUrl(ttsRes.getAudioUrl());
                    segmentAudio.setAudioDuration(ttsRes.getDuration());

                    long segmentDuration = ttsRes.getDuration();
                    log.info("第 {} 个段落音频生成成功，实际时长: {}ms", i + 1, segmentDuration);

                    // 检查是否可以放入当前分镜
                    if (accumulatedDuration + segmentDuration <= remainingDuration) {
                        accumulatedDuration += segmentDuration;
                        splitIndex = i + 1;
                        log.info("第 {} 个段落可以放入当前分镜，累积时长: {}ms", i + 1, accumulatedDuration);
                    } else {
                        log.info("第 {} 个段落会超出剩余时长，停止累积", i + 1);
                        // 不更新splitIndex，这个音频将放到下一个分镜
                    }
                } else {
                    log.error("第 {} 个段落音频生成失败，使用估算时长", i + 1);
                    // 如果TTS生成失败，使用估算时长
                    long estimatedDuration = TextSplitUtils.estimateAudioDuration(segment);
                    segmentAudio.setAudioDuration(estimatedDuration);

                    if (accumulatedDuration + estimatedDuration <= remainingDuration) {
                        accumulatedDuration += estimatedDuration;
                        splitIndex = i + 1;
                        log.info("第 {} 个段落使用估算时长可以放入当前分镜，累积时长: {}ms", i + 1, accumulatedDuration);
                    } else {
                        log.info("第 {} 个段落使用估算时长会超出剩余时长，停止累积", i + 1);
                    }
                }
            } catch (Exception e) {
                log.error("生成第 {} 个段落音频时发生异常: {}", i + 1, e.getMessage(), e);
                // 如果TTS生成异常，使用估算时长
                long estimatedDuration = TextSplitUtils.estimateAudioDuration(segment);
                segmentAudio.setAudioDuration(estimatedDuration);

                if (accumulatedDuration + estimatedDuration <= remainingDuration) {
                    accumulatedDuration += estimatedDuration;
                    splitIndex = i + 1;
                    log.info("第 {} 个段落异常后使用估算时长可以放入当前分镜，累积时长: {}ms", i + 1, accumulatedDuration);
                } else {
                    log.info("第 {} 个段落异常后使用估算时长会超出剩余时长，停止累积", i + 1);
                }
            }

            // 将生成的音频添加到列表中，无论是否能放入当前分镜
            generatedAudios.add(segmentAudio);
        }

        log.info("音频生成完成，共生成 {} 个音频，当前分镜将包含前 {} 个音频，累积时长: {}ms",
                generatedAudios.size(), splitIndex, accumulatedDuration);

        // 将前splitIndex个音频放入当前分镜
        for (int i = 0; i < splitIndex; i++) {
            currentShotAudios.add(generatedAudios.get(i));
            log.info("将第 {} 个音频放入当前分镜: {}", i + 1, generatedAudios.get(i).getText());
        }

        // 将剩余的音频放入下一个分镜
        for (int i = splitIndex; i < generatedAudios.size(); i++) {
            nextShotAudios.add(generatedAudios.get(i));
            log.info("将第 {} 个音频放入下一个分镜: {}", i + 1, generatedAudios.get(i).getText());
        }
    }

    /**
     * 从原始音频创建新的音频对象
     */
    private AiCanvasAudioPo createAudioFromOriginal(AiCanvasAudioPo original, String newText) {
        AiCanvasAudioPo newAudio = new AiCanvasAudioPo();
        BeanUtils.copyProperties(original, newAudio);
        newAudio.setId(null); // 清空ID，让数据库自动生成
        newAudio.setText(newText);
        newAudio.setAudioUrl(null); // 清空URL，需要重新生成
        newAudio.setAudioDuration(null); // 清空时长，由调用方设置实际时长
        return newAudio;
    }

    /**
     * 为分镜重新生成音频
     */
    private void regenerateAudiosForShot(AiCanvasShotPo shot, List<AiCanvasAudioPo> audioList) {
        if (audioList.isEmpty()) {
            return;
        }

        log.info("为分镜 {} 重新生成 {} 个音频", shot.getCode(), audioList.size());

        for (int i = 0; i < audioList.size(); i++) {
            AiCanvasAudioPo audio = audioList.get(i);
            audio.setCanvasId(shot.getCanvasId());
            audio.setShotCode(shot.getCode());
            audio.setSortOrder(i + 1);
            audio.setCreateTime(new Date());
            audio.setUpdateTime(new Date());
            audio.setDelFlag(0);

            // 如果是旁白类型且需要重新生成音频
            if (audio.getAudioType() == 1 && !StringUtils.hasText(audio.getAudioUrl())) {
                try {
                    // 调用TTS生成音频
                    TtsGenerateReq ttsReq = new TtsGenerateReq();
                    ttsReq.setConversationId(shot.getCode());
                    ttsReq.setText(audio.getText());
                    ttsReq.setVoiceId(audio.getVoiceId());
                    ttsReq.setContentId(shot.getId().toString());
                    ttsReq.setSource(2);
                    ttsReq.setSoundType("narration");

                    TtsGenerateRes ttsRes = aiCreationContentService.generateTts(ttsReq);
                    if (ttsRes != null) {
                        audio.setAudioUrl(ttsRes.getAudioUrl());
                        audio.setAudioDuration(ttsRes.getDuration());
                    }
                } catch (Exception e) {
                    log.error("重新生成音频失败，分镜: {}, 文本: {}", shot.getCode(), audio.getText(), e);
                }
            }

            audio.setId(null);
            audio.setUpdateTime(new Date());
            audio.setCreateTime(new Date());
            canvasAudioMapper.insert(audio);
        }

        log.info("分镜 {} 音频重新生成完成", shot.getCode());
    }

    /**
     * 更新后续分镜的排序序号
     */
    private void updateSubsequentShotOrders(Long canvasId, Integer currentSortOrder) {
        LambdaUpdateWrapper<AiCanvasShotPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AiCanvasShotPo::getCanvasId, canvasId)
                .gt(AiCanvasShotPo::getSortOrder, currentSortOrder)
                .eq(AiCanvasShotPo::getDelFlag, 0)
                .setSql("sort_order = sort_order + 1");
        this.update(updateWrapper);
        log.info("更新画布 {} 中排序大于 {} 的分镜序号", canvasId, currentSortOrder);
    }

    /**
     * 生成分镜编码
     */
    private String generateShotCode(Long canvasId) {
        return "SHOT-" + canvasId + "-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}